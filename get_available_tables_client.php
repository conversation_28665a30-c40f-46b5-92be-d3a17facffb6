<?php
// Include configuration and functions
include 'config/config.php';
include 'includes/functions.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if required parameters are provided
if (!isset($_GET['date']) || empty($_GET['date'])) {
    echo json_encode(['error' => 'Date parameter is required']);
    exit;
}

if (!isset($_GET['time']) || empty($_GET['time'])) {
    echo json_encode(['error' => 'Time parameter is required']);
    exit;
}

if (!isset($_GET['guests']) || empty($_GET['guests'])) {
    echo json_encode(['error' => 'Guests parameter is required']);
    exit;
}

// Get and sanitize parameters
$date = sanitize($_GET['date']);
$time = sanitize($_GET['time']);
$guests = (int)$_GET['guests'];

// Make sure date is in YYYY-MM-DD format
if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
    // Try to convert from MM/DD/YYYY format
    $date_parts = explode('/', $date);
    if (count($date_parts) === 3) {
        $month = str_pad($date_parts[0], 2, '0', STR_PAD_LEFT);
        $day = str_pad($date_parts[1], 2, '0', STR_PAD_LEFT);
        $year = $date_parts[2];
        $date = "$year-$month-$day";
    } else {
        echo json_encode(['error' => 'Invalid date format']);
        exit;
    }
}

// Make sure time is in HH:MM:SS format
if (!preg_match('/^\d{2}:\d{2}:\d{2}$/', $time)) {
    // Try to convert from 12-hour format (h:mm AM/PM)
    if (preg_match('/(\d{1,2}):(\d{2}) (AM|PM)/i', $time, $matches)) {
        $hours = (int)$matches[1];
        $minutes = $matches[2];
        $ampm = strtoupper($matches[3]);

        // Convert to 24-hour format
        if ($ampm === 'PM' && $hours < 12) {
            $hours += 12;
        } elseif ($ampm === 'AM' && $hours === 12) {
            $hours = 0;
        }

        $time = sprintf('%02d:%02d:00', $hours, $minutes);
    } else {
        echo json_encode(['error' => 'Invalid time format']);
        exit;
    }
}

// Get available tables for the given date, time, and party size
$available_tables = getAvailableTables($date, $time, $guests);

// Find optimal table combination
$optimal_tables = [];
if (!empty($available_tables)) {
    $optimal_tables = findOptimalTables($available_tables, $guests);
}

// Return the available tables and optimal tables
echo json_encode([
    'available_tables' => $available_tables,
    'optimal_tables' => $optimal_tables,
    'date' => $date,
    'time' => $time,
    'guests' => $guests
]);
?>
