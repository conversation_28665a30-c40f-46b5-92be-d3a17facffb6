<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Restaurant Management System ERD</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        h1, h2 {
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .entity {
            border: 1px solid #333;
            border-radius: 5px;
            margin-bottom: 20px;
            background-color: #f9f9f9;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .entity-header {
            background-color: #4a6fa5;
            color: white;
            padding: 10px;
            font-weight: bold;
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
        }
        .entity-body {
            padding: 10px;
        }
        .attribute {
            margin: 5px 0;
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        .pk {
            font-weight: bold;
            color: #c00;
        }
        .fk {
            font-weight: bold;
            color: #0a0;
        }
        .relationships {
            margin-top: 30px;
            padding: 15px;
            background-color: #f0f0f0;
            border-radius: 5px;
        }
        .relationship {
            margin: 10px 0;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Restaurant Management System - Entity Relationship Diagram</h1>
        
        <div class="grid">
            <!-- Users Entity -->
            <div class="entity">
                <div class="entity-header">Users</div>
                <div class="entity-body">
                    <div class="attribute"><span class="pk">id</span> INT (Primary Key)</div>
                    <div class="attribute">name VARCHAR(100) NOT NULL</div>
                    <div class="attribute">email VARCHAR(100) NOT NULL UNIQUE</div>
                    <div class="attribute">password VARCHAR(255) NOT NULL</div>
                    <div class="attribute">phone VARCHAR(20)</div>
                    <div class="attribute">address TEXT</div>
                    <div class="attribute">created_at TIMESTAMP</div>
                </div>
            </div>

            <!-- Admin Entity -->
            <div class="entity">
                <div class="entity-header">Admin</div>
                <div class="entity-body">
                    <div class="attribute"><span class="pk">id</span> INT (Primary Key)</div>
                    <div class="attribute">username VARCHAR(50) NOT NULL UNIQUE</div>
                    <div class="attribute">password VARCHAR(255) NOT NULL</div>
                    <div class="attribute">name VARCHAR(100)</div>
                    <div class="attribute">email VARCHAR(100)</div>
                    <div class="attribute">created_at TIMESTAMP</div>
                </div>
            </div>

            <!-- Menu Items Entity -->
            <div class="entity">
                <div class="entity-header">Menu Items</div>
                <div class="entity-body">
                    <div class="attribute"><span class="pk">id</span> INT (Primary Key)</div>
                    <div class="attribute">name VARCHAR(100) NOT NULL</div>
                    <div class="attribute">category VARCHAR(50) NOT NULL</div>
                    <div class="attribute">price DECIMAL(10,2) NOT NULL</div>
                    <div class="attribute">image VARCHAR(255)</div>
                    <div class="attribute">description TEXT</div>
                    <div class="attribute">created_at TIMESTAMP</div>
                </div>
            </div>

            <!-- Orders Entity -->
            <div class="entity">
                <div class="entity-header">Orders</div>
                <div class="entity-body">
                    <div class="attribute"><span class="pk">id</span> INT (Primary Key)</div>
                    <div class="attribute"><span class="fk">user_id</span> INT (Foreign Key)</div>
                    <div class="attribute">items TEXT NOT NULL</div>
                    <div class="attribute">total_price DECIMAL(10,2) NOT NULL</div>
                    <div class="attribute">payment_method ENUM</div>
                    <div class="attribute">status ENUM</div>
                    <div class="attribute">date TIMESTAMP</div>
                </div>
            </div>

            <!-- Order Items Entity -->
            <div class="entity">
                <div class="entity-header">Order Items</div>
                <div class="entity-body">
                    <div class="attribute"><span class="pk">id</span> INT (Primary Key)</div>
                    <div class="attribute"><span class="fk">order_id</span> INT (Foreign Key)</div>
                    <div class="attribute"><span class="fk">menu_item_id</span> INT (Foreign Key)</div>
                    <div class="attribute">quantity INT NOT NULL</div>
                    <div class="attribute">price DECIMAL(10,2) NOT NULL</div>
                    <div class="attribute">created_at TIMESTAMP</div>
                </div>
            </div>

            <!-- Reservations Entity -->
            <div class="entity">
                <div class="entity-header">Reservations</div>
                <div class="entity-body">
                    <div class="attribute"><span class="pk">id</span> INT (Primary Key)</div>
                    <div class="attribute"><span class="fk">user_id</span> INT (Foreign Key)</div>
                    <div class="attribute">date DATE NOT NULL</div>
                    <div class="attribute">time TIME NOT NULL</div>
                    <div class="attribute">guests INT NOT NULL</div>
                    <div class="attribute">status ENUM</div>
                    <div class="attribute">created_at TIMESTAMP</div>
                </div>
            </div>

            <!-- Tables Entity -->
            <div class="entity">
                <div class="entity-header">Tables</div>
                <div class="entity-body">
                    <div class="attribute"><span class="pk">id</span> INT (Primary Key)</div>
                    <div class="attribute">capacity INT NOT NULL</div>
                    <div class="attribute">is_reserved BOOLEAN</div>
                </div>
            </div>

            <!-- Receipts Entity -->
            <div class="entity">
                <div class="entity-header">Receipts</div>
                <div class="entity-body">
                    <div class="attribute"><span class="pk">id</span> INT (Primary Key)</div>
                    <div class="attribute"><span class="fk">order_id</span> INT (Foreign Key)</div>
                    <div class="attribute">file_path VARCHAR(255)</div>
                    <div class="attribute">created_at TIMESTAMP</div>
                </div>
            </div>

            <!-- Email Logs Entity -->
            <div class="entity">
                <div class="entity-header">Email Logs</div>
                <div class="entity-body">
                    <div class="attribute"><span class="pk">id</span> INT (Primary Key)</div>
                    <div class="attribute"><span class="fk">user_id</span> INT (Foreign Key)</div>
                    <div class="attribute">type ENUM</div>
                    <div class="attribute">status ENUM</div>
                    <div class="attribute">timestamp TIMESTAMP</div>
                </div>
            </div>

            <!-- Payment Transactions Entity -->
            <div class="entity">
                <div class="entity-header">Payment Transactions</div>
                <div class="entity-body">
                    <div class="attribute"><span class="pk">id</span> INT (Primary Key)</div>
                    <div class="attribute"><span class="fk">order_id</span> INT (Foreign Key)</div>
                    <div class="attribute">amount DECIMAL(10,2) NOT NULL</div>
                    <div class="attribute">payment_method ENUM</div>
                    <div class="attribute">transaction_status ENUM</div>
                    <div class="attribute">transaction_date TIMESTAMP</div>
                    <div class="attribute">transaction_reference VARCHAR(100)</div>
                    <div class="attribute">notes TEXT</div>
                </div>
            </div>

            <!-- Refunds Entity -->
            <div class="entity">
                <div class="entity-header">Refunds</div>
                <div class="entity-body">
                    <div class="attribute"><span class="pk">id</span> INT (Primary Key)</div>
                    <div class="attribute"><span class="fk">payment_transaction_id</span> INT (Foreign Key)</div>
                    <div class="attribute">amount DECIMAL(10,2) NOT NULL</div>
                    <div class="attribute">reason TEXT</div>
                    <div class="attribute">status ENUM</div>
                    <div class="attribute">refund_date TIMESTAMP</div>
                    <div class="attribute"><span class="fk">processed_by</span> INT (Foreign Key)</div>
                    <div class="attribute">notes TEXT</div>
                </div>
            </div>
        </div>

        <div class="relationships">
            <h2>Relationships</h2>
            <div class="relationship">1. <strong>Users to Orders</strong>: One-to-Many (A user can place many orders)</div>
            <div class="relationship">2. <strong>Users to Reservations</strong>: One-to-Many (A user can make many reservations)</div>
            <div class="relationship">3. <strong>Orders to Order Items</strong>: One-to-Many (An order can contain many order items)</div>
            <div class="relationship">4. <strong>Menu Items to Order Items</strong>: One-to-Many (A menu item can be in many order items)</div>
            <div class="relationship">5. <strong>Orders to Receipts</strong>: One-to-One (An order has one receipt)</div>
            <div class="relationship">6. <strong>Orders to Payment Transactions</strong>: One-to-Many (An order can have multiple payment transactions)</div>
            <div class="relationship">7. <strong>Payment Transactions to Refunds</strong>: One-to-Many (A payment transaction can have multiple refunds)</div>
            <div class="relationship">8. <strong>Admin to Refunds</strong>: One-to-Many (An admin can process many refunds)</div>
            <div class="relationship">9. <strong>Users to Email Logs</strong>: One-to-Many (A user can have many email logs)</div>
        </div>
    </div>
</body>
</html>
