<?php
session_start();
include '../config/config.php';
include '../includes/functions.php';

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

// Set content type to JSON
header('Content-Type: application/json');

// Check if reservation_id parameter is provided
if (!isset($_GET['reservation_id']) || empty($_GET['reservation_id'])) {
    echo json_encode(['error' => 'Reservation ID parameter is required']);
    exit;
}

// Get and sanitize reservation_id parameter
$reservation_id = (int)$_GET['reservation_id'];

// Get tables assigned to the reservation
$tables = getTablesForReservation($reservation_id);

// Return the tables
echo json_encode(['tables' => $tables]);
?>
