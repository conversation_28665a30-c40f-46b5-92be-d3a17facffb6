<?php

function notifyAdminNewOrder($order_id, $order_details, $user) {
    $admin_email = ADMIN_EMAIL;
    $subject = 'New Order Notification - ' . SITE_NAME;

    $message = "Hello Administrator,\n\n";
    $message .= "A new order has been placed on " . SITE_NAME . ".\n\n";
    $message .= "Order ID: " . $order_id . "\n";
    $message .= "Order Date: " . date('F j, Y, g:i a') . "\n";
    $message .= "Payment Method: " . ucwords(str_replace('_', ' ', $order_details['payment_method'])) . "\n\n";

    $message .= "Customer Information:\n";
    $message .= "Name: " . ($user ? $user['name'] : 'Guest') . "\n";
    $message .= "Email: " . ($user ? $user['email'] : 'N/A') . "\n";
    $message .= "Phone: " . ($user && isset($user['phone']) ? $user['phone'] : 'N/A') . "\n\n";

    $message .= "Order Details:\n";
    $total = 0;

    foreach ($order_details['items'] as $item) {
        $item_total = $item['price'] * $item['quantity'];
        $total += $item_total;
        $message .= $item['name'] . " x " . $item['quantity'] . " - " . formatCurrency($item_total) . "\n";
    }

    $message .= "\nTotal Amount: " . formatCurrency($total) . "\n\n";

    $message .= "You can view and manage this order in the admin panel:\n";
    $message .= SITE_URL . "/admin/orders.php\n\n";

    $message .= "Thank you,\n";
    $message .= SITE_NAME . " System";

    $html_message = nl2br($message);

    return sendEmail($admin_email, $subject, $message, 0, 'admin_notification', $html_message);
}

function notifyAdminNewReservation($reservation_id, $reservation_details, $user) {
    $admin_email = ADMIN_EMAIL;
    $subject = 'New Reservation Notification - ' . SITE_NAME;

    $message = "Hello Administrator,\n\n";
    $message .= "A new reservation has been made on " . SITE_NAME . ".\n\n";
    $message .= "Reservation ID: " . $reservation_id . "\n";
    $message .= "Date: " . formatDate($reservation_details['date']) . "\n";
    $message .= "Time: " . formatTime($reservation_details['time']) . "\n";
    $message .= "Number of Guests: " . $reservation_details['guests'] . "\n";

    if (isset($reservation_details['tables']) && !empty($reservation_details['tables'])) {
        $message .= "Assigned Tables: ";
        foreach ($reservation_details['tables'] as $index => $table) {
            $message .= "Table #" . $table['id'] . " (Capacity: " . $table['capacity'] . ")";
            if ($index < count($reservation_details['tables']) - 1) {
                $message .= ", ";
            }
        }
        $message .= "\n";
    }

    $message .= "\n";

    $message .= "Customer Information:\n";
    $message .= "Name: " . ($user ? $user['name'] : 'Guest') . "\n";
    $message .= "Email: " . ($user ? $user['email'] : 'N/A') . "\n";
    $message .= "Phone: " . ($user && isset($user['phone']) ? $user['phone'] : 'N/A') . "\n\n";

    $message .= "You can view and manage this reservation in the admin panel:\n";
    $message .= SITE_URL . "/admin/reservations.php\n\n";

    $message .= "Thank you,\n";
    $message .= SITE_NAME . " System";

    $html_message = nl2br($message);

    return sendEmail($admin_email, $subject, $message, 0, 'admin_notification', $html_message);
}
?>
