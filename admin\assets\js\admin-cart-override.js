/**
 * Admin Cart Override - Prevents cart badges from appearing in admin area
 */

// Override the updateCartBadges function to do nothing in admin area
function updateCartBadges(count) {
    // Do nothing in admin area
    console.log("Cart badge update prevented in admin area");
    return;
}

// Override the incrementCartCount function to do nothing in admin area
function incrementCartCount(quantity) {
    // Do nothing in admin area
    console.log("Cart count increment prevented in admin area");
    return 0;
}

// Function to remove any existing cart badges
function removeExistingCartBadges() {
    // Find all cart badges
    const cartBadges = document.querySelectorAll('.cart-badge, .cart-icon-container .badge, .badge.bg-warning:not(.reservation-item .badge)');
    
    // Remove them
    cartBadges.forEach(badge => {
        badge.remove();
    });
}

// Run when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Remove any existing cart badges
    removeExistingCartBadges();
    
    // Run periodically to catch any dynamically added badges
    setInterval(removeExistingCartBadges, 500);
});
