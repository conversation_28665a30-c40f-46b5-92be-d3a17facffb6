<?php
// Initialize cart if it doesn't exist
if (!isset($_SESSION['cart'])) {
    $_SESSION['cart'] = [];
}

// Handle cart actions
if (isset($_GET['action'])) {
    $action = $_GET['action'];

    // Add item to cart
    if ($action == 'add' && isset($_POST['item_id']) && isset($_POST['quantity'])) {
        $item_id = $_POST['item_id'];
        $quantity = (int)$_POST['quantity'];

        // Get item details
        $item = getMenuItemById($item_id);

        if ($item && $quantity > 0) {
            // Check if item already exists in cart
            $item_exists = false;
            foreach ($_SESSION['cart'] as $key => $cart_item) {
                if ($cart_item['id'] == $item_id) {
                    // Update quantity
                    $_SESSION['cart'][$key]['quantity'] += $quantity;
                    $item_exists = true;
                    break;
                }
            }

            // Add new item to cart
            if (!$item_exists) {
                $_SESSION['cart'][] = [
                    'id' => $item['id'],
                    'name' => $item['name'],
                    'price' => $item['price'],
                    'image' => $item['image'],
                    'quantity' => $quantity
                ];
            }

            // If AJAX request, return JSON response
            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                // Calculate the total number of items in the cart
                $total_items = 0;
                foreach ($_SESSION['cart'] as $cart_item) {
                    $total_items += $cart_item['quantity'];
                }

                // Debug output to verify the cart contents and count
                error_log("Cart contents: " . print_r($_SESSION['cart'], true));
                error_log("Total items in cart: " . $total_items);

                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'message' => 'Item added to cart successfully!',
                    'cart_count' => $total_items, // Use total items count instead of just array count
                    'cart_unique_count' => count($_SESSION['cart']), // Also include unique item count
                    'item_name' => $item['name'],
                    'item_quantity' => $quantity,
                    'cart_items' => $_SESSION['cart'] // Include the full cart for debugging
                ]);
                exit;
            }

            // Set success message - this will be displayed at the top of the page
            $_SESSION['message'] = $quantity . 'x ' . $item['name'] . ' added to cart successfully!';
            $_SESSION['message_type'] = 'success';

            // Set a specific message for the added item (for non-AJAX fallback)
            $_SESSION['cart_notification'] = [
                'item_name' => $item['name'],
                'quantity' => $quantity,
                'timestamp' => time()
            ];

            // Redirect to the same page to show the notification
            if (isset($_SERVER['HTTP_REFERER'])) {
                header('Location: ' . $_SERVER['HTTP_REFERER']);
                exit;
            }
        }
    }

    // Update cart item quantity
    else if ($action == 'update' && isset($_POST['item_id']) && isset($_POST['quantity'])) {
        $item_id = $_POST['item_id'];
        $quantity = (int)$_POST['quantity'];

        if ($quantity > 0) {
            foreach ($_SESSION['cart'] as $key => $cart_item) {
                if ($cart_item['id'] == $item_id) {
                    $_SESSION['cart'][$key]['quantity'] = $quantity;
                    break;
                }
            }
        }

        // Set success message
        $_SESSION['message'] = 'Cart updated successfully!';
        $_SESSION['message_type'] = 'success';
    }

    // Remove item from cart
    else if ($action == 'remove' && isset($_GET['item_id'])) {
        $item_id = $_GET['item_id'];

        foreach ($_SESSION['cart'] as $key => $cart_item) {
            if ($cart_item['id'] == $item_id) {
                unset($_SESSION['cart'][$key]);
                break;
            }
        }

        // Reindex array
        $_SESSION['cart'] = array_values($_SESSION['cart']);

        // Set success message
        $_SESSION['message'] = 'Item removed from cart successfully!';
        $_SESSION['message_type'] = 'success';
    }

    // Clear cart
    else if ($action == 'clear') {
        $_SESSION['cart'] = [];

        // Set success message
        $_SESSION['message'] = 'Cart cleared successfully!';
        $_SESSION['message_type'] = 'success';
    }

    // Place order
    else if ($action == 'place_order') {
        // Check if cart is empty
        if (empty($_SESSION['cart'])) {
            $_SESSION['message'] = 'Your cart is empty!';
            $_SESSION['message_type'] = 'danger';
            redirect('index.php?page=cart');
        }

        // Check if user is logged in
        if (!isLoggedIn()) {
            $_SESSION['message'] = 'Please login to place an order!';
            $_SESSION['message_type'] = 'danger';
            redirect('index.php?page=login');
        }

        // Calculate total price
        $total_price = 0;
        foreach ($_SESSION['cart'] as $cart_item) {
            $total_price += $cart_item['price'] * $cart_item['quantity'];
        }

        // Get payment method
        $payment_method = isset($_POST['payment_method']) ? sanitize($_POST['payment_method']) : 'cash';

        // Start transaction
        $conn->begin_transaction();

        try {
            // Insert order into database (still keep items for backward compatibility)
            $items = json_encode($_SESSION['cart']);
            $stmt = $conn->prepare("INSERT INTO orders (user_id, items, total_price, payment_method, status) VALUES (?, ?, ?, ?, 'pending')");
            $stmt->bind_param("isds", $_SESSION['user_id'], $items, $total_price, $payment_method);
            $stmt->execute();
            $order_id = $stmt->insert_id;

            // Insert order items into order_items table
            $stmt = $conn->prepare("INSERT INTO order_items (order_id, menu_item_id, quantity, price) VALUES (?, ?, ?, ?)");

            foreach ($_SESSION['cart'] as $cart_item) {
                $menu_item_id = $cart_item['id'];
                $quantity = $cart_item['quantity'];
                $price = $cart_item['price'];

                $stmt->bind_param("iiid", $order_id, $menu_item_id, $quantity, $price);
                $stmt->execute();
            }

            // Create a payment transaction for this order
            $transaction_data = [
                'order_id' => $order_id,
                'amount' => $total_price,
                'payment_method' => $payment_method,
                'transaction_status' => 'pending',
                'transaction_reference' => 'ORD-' . $order_id . '-' . date('YmdHis'),
                'notes' => 'Created from customer order'
            ];

            createPaymentTransaction($transaction_data);

            // Commit transaction
            $conn->commit();

            // Get user email
            $user = getUserById($_SESSION['user_id']);

            // Send email notification to customer
            $subject = 'Order Confirmation - ' . SITE_NAME;
            $message = "Dear " . $user['name'] . ",\n\n";
            $message .= "Thank you for your order at " . SITE_NAME . "!\n\n";
            $message .= "Order ID: " . $order_id . "\n";
            $message .= "Order Date: " . date('F j, Y, g:i a') . "\n";
            $message .= "Payment Method: " . ucwords(str_replace('_', ' ', $payment_method)) . "\n\n";
            $message .= "Order Details:\n";

            // Include admin notifications
            require_once __DIR__ . '/../includes/admin_notifications.php';

            foreach ($_SESSION['cart'] as $cart_item) {
                $message .= $cart_item['name'] . " x " . $cart_item['quantity'] . " - " . formatCurrency($cart_item['price'] * $cart_item['quantity']) . "\n";
            }

            $message .= "\nTotal: " . formatCurrency($total_price) . "\n\n";
            $message .= "Your order is being processed and will be ready soon.\n\n";
            $message .= "Thank you for choosing " . SITE_NAME . "!\n";

            // Send email to customer
            sendEmail($user['email'], $subject, $message, $_SESSION['user_id'], 'order');

            // Send notification to admin about the new order
            $order_details = [
                'payment_method' => $payment_method,
                'items' => $_SESSION['cart']
            ];
            notifyAdminNewOrder($order_id, $order_details, $user);

            // Clear cart
            $_SESSION['cart'] = [];

            // Set success message
            $_SESSION['message'] = 'Order placed successfully! Your order #' . $order_id . ' has been received.';
            $_SESSION['message_type'] = 'success';

            // Redirect to profile page
            redirect('index.php?page=profile');

        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollback();

            // Set error message
            $_SESSION['message'] = 'Failed to place order: ' . $e->getMessage();
            $_SESSION['message_type'] = 'danger';

            // Log the error
            error_log('Order placement failed: ' . $e->getMessage());

            // Redirect back to cart page
            redirect('index.php?page=cart');
        }
    }

    // Redirect back to cart page if not already there
    if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) != 'xmlhttprequest') {
        redirect('index.php?page=cart');
    }
}
?>

<div class="container">
    <h1 class="mb-4">Your Cart</h1>

    <?php if (empty($_SESSION['cart'])): ?>
        <div class="alert alert-info">
            Your cart is empty. <a href="<?php echo SITE_URL; ?>/index.php?page=menu">Browse our menu</a> to add items.
        </div>
    <?php else: ?>
        <div class="row">
            <div class="col-lg-8">
                <!-- Cart Items -->
                <?php foreach ($_SESSION['cart'] as $cart_item): ?>
                    <div class="card mb-3 cart-item">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-md-2 col-4">
                                    <img src="<?php echo !empty($cart_item['image']) ? SITE_URL . '/uploads/' . $cart_item['image'] : SITE_URL . '/assets/images/default-food.jpg'; ?>" alt="<?php echo $cart_item['name']; ?>" class="img-fluid rounded cart-item-image">
                                </div>
                                <div class="col-md-4 col-8">
                                    <h5 class="card-title"><?php echo $cart_item['name']; ?></h5>
                                    <p class="card-text item-price" data-price="<?php echo $cart_item['price']; ?>">₱ <?php echo formatCurrency($cart_item['price']); ?> each</p>
                                </div>
                                <div class="col-md-3 col-6 mt-3 mt-md-0">
                                    <form method="post" action="<?php echo SITE_URL; ?>/index.php?page=cart&action=update">
                                        <input type="hidden" name="item_id" value="<?php echo $cart_item['id']; ?>">
                                        <div class="input-group">
                                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="this.parentNode.querySelector('input[type=number]').stepDown(); this.form.submit();">-</button>
                                            <input type="number" name="quantity" class="form-control form-control-sm text-center quantity-input" value="<?php echo $cart_item['quantity']; ?>" min="1" onchange="this.form.submit()">
                                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="this.parentNode.querySelector('input[type=number]').stepUp(); this.form.submit();">+</button>
                                        </div>
                                    </form>
                                </div>
                                <div class="col-md-2 col-6 text-end mt-3 mt-md-0">
                                    <p class="card-text fw-bold item-total">₱ <?php echo formatCurrency($cart_item['price'] * $cart_item['quantity']); ?></p>
                                </div>
                                <div class="col-md-1 col-12 text-end mt-3 mt-md-0">
                                    <a href="<?php echo SITE_URL; ?>/index.php?page=cart&action=remove&item_id=<?php echo $cart_item['id']; ?>" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to remove this item?');">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>

                <div class="d-flex justify-content-between mb-4">
                    <a href="<?php echo SITE_URL; ?>/index.php?page=menu" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left"></i> Continue Shopping
                    </a>
                    <a href="<?php echo SITE_URL; ?>/index.php?page=cart&action=clear" class="btn btn-outline-danger" onclick="return confirm('Are you sure you want to clear your cart?');">
                        <i class="fas fa-trash"></i> Clear Cart
                    </a>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Cart Summary -->
                <div class="card cart-summary">
                    <div class="card-body">
                        <h5 class="card-title">Order Summary</h5>
                        <hr>

                        <?php
                        $total = 0;
                        foreach ($_SESSION['cart'] as $cart_item) {
                            $total += $cart_item['price'] * $cart_item['quantity'];
                        }
                        ?>

                        <hr>

                        <div class="d-flex justify-content-between mb-4">
                            <span class="fw-bold">Total:</span>
                            <span class="fw-bold" id="cart-total">₱ <?php echo formatCurrency($total); ?></span>
                        </div>

                        <form method="post" action="<?php echo SITE_URL; ?>/index.php?page=cart&action=place_order">
                            <div class="mb-3">
                                <label for="payment_method" class="form-label">Payment Method</label>
                                <select class="form-select" id="payment_method" name="payment_method" required>
                                    <option value="cash">Cash</option>
                                    <option value="credit_card">Credit Card</option>
                                    <option value="debit_card">Debit Card</option>
                                    <option value="gcash">GCash</option>
                                    <option value="paymaya">PayMaya</option>
                                </select>
                            </div>

                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-check"></i> Place Order
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>
