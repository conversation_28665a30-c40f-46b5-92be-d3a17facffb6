/* Hide cart badges in admin area */
.cart-badge,
.cart-icon-container .badge,
.badge.bg-warning,
.badge.bg-danger,
span[class*="badge"][style*="display: flex"],
.fa-shopping-cart + .badge {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    width: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
    position: absolute !important;
    z-index: -9999 !important;
}

/* Hide cart icon containers in admin area but keep the icons */
.cart-icon-container {
    display: inline-block !important;
    position: relative !important;
}

/* Target any yellow badges specifically */
.badge.bg-warning {
    display: none !important;
}
