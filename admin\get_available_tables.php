<?php
session_start();
include '../config/config.php';
include '../includes/functions.php';

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

// Set content type to JSON
header('Content-Type: application/json');

// Check if required parameters are provided
if (!isset($_GET['reservation_id']) || empty($_GET['reservation_id'])) {
    echo json_encode(['error' => 'Reservation ID parameter is required']);
    exit;
}

if (!isset($_GET['date']) || empty($_GET['date'])) {
    echo json_encode(['error' => 'Date parameter is required']);
    exit;
}

if (!isset($_GET['time']) || empty($_GET['time'])) {
    echo json_encode(['error' => 'Time parameter is required']);
    exit;
}

if (!isset($_GET['guests']) || empty($_GET['guests'])) {
    echo json_encode(['error' => 'Guests parameter is required']);
    exit;
}

// Get and sanitize parameters
$reservation_id = (int)$_GET['reservation_id'];
$date = sanitize($_GET['date']);
$time = sanitize($_GET['time']);
$guests = (int)$_GET['guests'];

// Get available tables for the given date, time, and party size
// Exclude the current reservation to avoid conflicts
$available_tables = getAvailableTables($date, $time, $guests, $reservation_id);

// Return the available tables
echo json_encode(['available_tables' => $available_tables]);
?>
