<?php
session_start();
include '../config/config.php';
include '../includes/functions.php';

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

// Set content type to JSON
header('Content-Type: application/json');

// Get table ID if provided
$table_id = isset($_GET['table_id']) ? (int)$_GET['table_id'] : null;

// Get current date
$current_date = date('Y-m-d');

// Build query based on whether a specific table is requested
if ($table_id) {
    // Get data for a specific table
    $query = "
        SELECT t.*,
               CASE WHEN rt.table_id IS NOT NULL THEN 1 ELSE 0 END AS is_reserved_today,
               r.id AS reservation_id,
               r.time AS reservation_time,
               r.guests AS reservation_guests,
               r.status AS reservation_status,
               r.user_id AS user_id,
               u.name AS customer_name,
               GROUP_CONCAT(
                   DISTINCT CONCAT(r2.id, ':', r2.time, ':', r2.guests, ':', r2.status, ':', COALESCE(u2.name, 'Guest'), ':', COALESCE(r2.user_id, '0'))
                   ORDER BY r2.time ASC
                   SEPARATOR '|'
               ) AS all_reservations
        FROM tables t
        LEFT JOIN reservation_tables rt ON t.id = rt.table_id
        LEFT JOIN reservations r ON rt.reservation_id = r.id AND r.date = ? AND r.status IN ('pending', 'confirmed')
        LEFT JOIN users u ON r.user_id = u.id
        LEFT JOIN reservation_tables rt2 ON t.id = rt2.table_id
        LEFT JOIN reservations r2 ON rt2.reservation_id = r2.id AND r2.date = ? AND r2.status IN ('pending', 'confirmed')
        LEFT JOIN users u2 ON r2.user_id = u2.id
        WHERE t.id = ?
        GROUP BY t.id
    ";

    $stmt = $conn->prepare($query);
    $stmt->bind_param("ssi", $current_date, $current_date, $table_id);
} else {
    // Get data for all tables
    $query = "
        SELECT t.*,
               CASE WHEN rt.table_id IS NOT NULL THEN 1 ELSE 0 END AS is_reserved_today,
               r.id AS reservation_id,
               r.time AS reservation_time,
               r.guests AS reservation_guests,
               r.status AS reservation_status,
               r.user_id AS user_id,
               u.name AS customer_name,
               GROUP_CONCAT(
                   DISTINCT CONCAT(r2.id, ':', r2.time, ':', r2.guests, ':', r2.status, ':', COALESCE(u2.name, 'Guest'), ':', COALESCE(r2.user_id, '0'))
                   ORDER BY r2.time ASC
                   SEPARATOR '|'
               ) AS all_reservations
        FROM tables t
        LEFT JOIN reservation_tables rt ON t.id = rt.table_id
        LEFT JOIN reservations r ON rt.reservation_id = r.id AND r.date = ? AND r.status IN ('pending', 'confirmed')
        LEFT JOIN users u ON r.user_id = u.id
        LEFT JOIN reservation_tables rt2 ON t.id = rt2.table_id
        LEFT JOIN reservations r2 ON rt2.reservation_id = r2.id AND r2.date = ? AND r2.status IN ('pending', 'confirmed')
        LEFT JOIN users u2 ON r2.user_id = u2.id
        GROUP BY t.id
        ORDER BY t.id
    ";

    $stmt = $conn->prepare($query);
    $stmt->bind_param("ss", $current_date, $current_date);
}

$stmt->execute();
$result = $stmt->get_result();

$tables = [];
while ($row = $result->fetch_assoc()) {
    // Process reservations to remove duplicates
    if (!empty($row['all_reservations'])) {
        $reservations = explode('|', $row['all_reservations']);
        $processed_reservations = [];
        $unique_reservations = [];

        foreach ($reservations as $reservation) {
            if (empty($reservation)) continue;
            list($res_id, $res_time, $res_guests, $res_status, $res_name, $user_id) = explode(':', $reservation);

            // Skip duplicate reservations
            if (in_array($res_id, $processed_reservations)) continue;
            $processed_reservations[] = $res_id;

            $unique_reservations[] = [
                'id' => $res_id,
                'user_id' => $user_id,
                'time' => $res_time,
                'guests' => (int)$res_guests,
                'status' => $res_status,
                'name' => $res_name
            ];
        }

        $row['reservations'] = $unique_reservations;
    } else {
        $row['reservations'] = [];
    }

    $tables[] = $row;
}

// Get table statistics
$total_tables = count($tables);
$reserved_tables = 0;
foreach ($tables as $table) {
    if ($table['is_reserved_today']) {
        $reserved_tables++;
    }
}
$available_tables = $total_tables - $reserved_tables;

// Return the data
$response = [
    'tables' => $table_id ? ($tables[0] ?? null) : $tables,
    'stats' => [
        'total_tables' => $total_tables,
        'reserved_tables' => $reserved_tables,
        'available_tables' => $available_tables
    ],
    'timestamp' => date('h:i:s A'),
    'date' => date('F j, Y', strtotime($current_date))
];

echo json_encode($response);
?>
